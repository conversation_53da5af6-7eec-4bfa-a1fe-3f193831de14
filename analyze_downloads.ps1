# Analyze user download behavior
$lines = Get-Content user_actions.sql | Select-String 'download_pdf'
$users = @{}
$userDownloadCounts = @{}

foreach ($line in $lines) {
    if ($line -match '\((\d+),\s*(\d+),\s*''download_pdf''') {
        $userId = $matches[2]
        $users[$userId] = $true

        if ($userDownloadCounts.ContainsKey($userId)) {
            $userDownloadCounts[$userId]++
        } else {
            $userDownloadCounts[$userId] = 1
        }
    }
}

$totalDownloads = $lines.Count
$totalUsers = $users.Count
$average = [math]::Round($totalDownloads / $totalUsers, 2)

Write-Host "=== Download Statistics ==="
Write-Host "Total downloads: $totalDownloads"
Write-Host "Total users: $totalUsers"
Write-Host "Average downloads per user: $average"

Write-Host ""
Write-Host "=== Top 10 Users by Downloads ==="
$topUsers = $userDownloadCounts.GetEnumerator() | Sort-Object Value -Descending | Select-Object -First 10
foreach ($user in $topUsers) {
    Write-Host "User $($user.Key): $($user.Value) downloads"
}

Write-Host ""
Write-Host "=== Download Distribution ==="
$distribution = $userDownloadCounts.Values | Group-Object | Sort-Object Name
foreach ($group in $distribution) {
    Write-Host "$($group.Name) downloads: $($group.Count) users"
}
